const axios = require('axios');
const fs = require('fs');

// 目标CSS内容（用于验证）
const TARGET_CSS_CONTENT = `@supports(-webkit-mask:none) and (not (cater-color:#fff)){.login-container .el-input input{color:#fff}}.login-container .el-input{display:inline-block;height:47px;width:85%}.login-container .el-input input{background:transparent;border:0;-webkit-appearance:none;border-radius:0;padding:12px 5px 12px 15px;color:#fff;height:47px;caret-color:#fff}.login-container .el-input input:-webkit-autofill{-webkit-box-shadow:0 0 0 1000px #283443 inset!important;box-shadow:inset 0 0 0 1000px #283443!important;-webkit-text-fill-color:#fff!important}.login-container .el-form-item{border:1px solid hsla(0,0%,100%,.1);background:rgba(0,0,0,.1);border-radius:5px;color:#454545}.home-page[data-v-1fd21fd4]{min-height:100%;width:100%;background-color:#2d3a4b}.home-page-menu[data-v-1fd21fd4]{width:100%;background-color:#283443}.top-menu-list[data-v-1fd21fd4]{width:580px;max-width:100%;margin:0 auto}.info-container .box-card[data-v-1fd21fd4]{min-height:100%;width:100%;background-color:#2d3a4b;position:relative;width:1000px;max-width:100%;padding:60px 35px 0;margin:0 auto;overflow:hidden;color:#fff}.login-container[data-v-1fd21fd4]{min-height:100%;width:100%;background-color:#2d3a4b;overflow:hidden}.login-container .login-form[data-v-1fd21fd4]{position:relative;width:520px;max-width:100%;padding:160px 35px 0;margin:0 auto;overflow:hidden}.login-container .tips[data-v-1fd21fd4]{font-size:14px;color:#fff;margin-bottom:10px}.login-container .tips span[data-v-1fd21fd4]:first-of-type{margin-right:16px}.login-container .svg-container[data-v-1fd21fd4]{padding:6px 5px 6px 15px;color:#889aa4;vertical-align:middle;width:30px;display:inline-block}.login-container .title-container[data-v-1fd21fd4]{position:relative}.login-container .title-container .title[data-v-1fd21fd4]{font-size:26px;color:#eee;margin:0 auto 40px auto;text-align:center;font-weight:700}.login-container .show-pwd[data-v-1fd21fd4]{position:absolute;right:10px;top:7px;font-size:16px;color:#889aa4;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}`;

// 存储找到的IP地址
const foundIPs = [];

// 配置选项
const config = {
    // IP范围配置 - 可以设置多种模式
    ipRanges: [
        // 模式1: 固定前两段IP
        {
            name: "175.27.x.x",
            type: "fixed",
            firstTwoSegments: [175, 27]
        }
    ],
    
    // 扫描设置
    timeout: 1000,        // 请求超时时间（毫秒）
    concurrency: 200,       // 并发请求数
    delay: 100,           // 批次间延迟（毫秒）
    
    // 目标URL路径
    targetPath: "/static/css/chunk-d4c1d6f4.157601f7.css"
};

// 检查单个IP地址
async function checkIP(ip) {
    const url = `http://${ip}${config.targetPath}`;
    
    try {
        //console.log(`正在检查: ${url}`);
        
        const response = await axios.get(url, {
            timeout: config.timeout,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });
        
        if (response.status === 200) {
            const content = response.data;
            
            // 检查内容是否匹配目标CSS
            if (content.trim() === TARGET_CSS_CONTENT.trim()) {
                console.log(`✅ 找到匹配的IP: ${ip}`);
                foundIPs.push({
                    ip: ip,
                    url: url,
                    timestamp: new Date().toISOString()
                });
                return true;
            } else {
                console.log(`❌ IP ${ip} 返回了不同的内容`);
            }
        }
    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            //console.log(`⏰ IP ${ip} 请求超时`);
        } else if (error.code === 'ECONNREFUSED') {
            //console.log(`�� IP ${ip} 连接被拒绝`);
        } else if (error.response) {
            //console.log(`❌ IP ${ip} 返回状态码: ${error.response.status}`);
        } else {
            //console.log(`❌ IP ${ip} 请求失败: ${error.message}`);
        }
    }
    
    return false;
}

// 生成IP地址列表
function generateIPs() {
    const allIPs = [];
    
    config.ipRanges.forEach(range => {
        console.log(`生成IP范围: ${range.name}`);
        
        switch (range.type) {
            case "fixed":
                // 固定前两段，枚举后两段
                const [a, b] = range.firstTwoSegments;
                for (let c = 0; c <= 255; c++) {
                    for (let d = 0; d <= 255; d++) {
                        allIPs.push(`${a}.${b}.${c}.${d}`);
                    }
                }
                break;
                
            case "enum_first":
                // 枚举第一段，固定第二段
                const firstSeg = range.firstSegment;
                const [secondStart, secondEnd] = range.secondSegmentRange;
                for (let b = secondStart; b <= secondEnd; b++) {
                    for (let c = 0; c <= 255; c++) {
                        for (let d = 0; d <= 255; d++) {
                            allIPs.push(`${firstSeg}.${b}.${c}.${d}`);
                        }
                    }
                }
                break;
                
            case "enum_both":
                // 枚举前两段
                const [firstStart, firstEnd] = range.firstSegmentRange;
                const [secondStart2, secondEnd2] = range.secondSegmentRange;
                for (let a = firstStart; a <= firstEnd; a++) {
                    for (let b = secondStart2; b <= secondEnd2; b++) {
                        for (let c = 0; c <= 255; c++) {
                            for (let d = 0; d <= 255; d++) {
                                allIPs.push(`${a}.${b}.${c}.${d}`);
                            }
                        }
                    }
                }
                break;
        }
    });
    
    return allIPs;
}

// 并发检查多个IP
async function scanIPs(ipList) {
    console.log(`\n开始扫描 ${ipList.length} 个IP地址，并发数: ${config.concurrency}`);
    
    const chunks = [];
    for (let i = 0; i < ipList.length; i += config.concurrency) {
        chunks.push(ipList.slice(i, i + config.concurrency));
    }
    
    for (let i = 0; i < chunks.length; i++) {
        console.log(`\n处理第 ${i + 1}/${chunks.length} 批 (${chunks[i].length} 个IP)`);
        
        const promises = chunks[i].map(ip => checkIP(ip));
        await Promise.all(promises);
        
        // 添加延迟避免过于频繁的请求
        if (config.delay > 0) {
            await new Promise(resolve => setTimeout(resolve, config.delay));
        }
    }
}

// 显示配置信息
function showConfig() {
    console.log('当前配置:');
    console.log(`- 超时时间: ${config.timeout}ms`);
    console.log(`- 并发数: ${config.concurrency}`);
    console.log(`- 批次延迟: ${config.delay}ms`);
    console.log(`- 目标路径: ${config.targetPath}`);
    console.log('\nIP范围配置:');
    
    config.ipRanges.forEach((range, index) => {
        console.log(`${index + 1}. ${range.name} (${range.type})`);
    });
}

// 主函数
async function main() {
    console.log('='.repeat(60));
    console.log('IP扫描器 - 最终版本');
    console.log('='.repeat(60));
    
    showConfig();
    
    console.log('\n⚠️  警告: 这将扫描大量IP地址，可能需要很长时间');
    console.log('按 Ctrl+C 可以随时停止扫描');
    console.log('\n开始生成IP列表...');
    
    const startTime = Date.now();
    
    try {
        // 生成所有IP地址
        const ipList = generateIPs();
        console.log(`\n总共生成 ${ipList.length} 个IP地址`);
        
        if (ipList.length > 1000000) {
            console.log('⚠️  警告: IP数量超过100万，建议缩小扫描范围');
        }
        
        console.log('\n开始扫描...');
        await scanIPs(ipList);
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log('\n' + '='.repeat(60));
        console.log('扫描完成!');
        console.log(`耗时: ${duration.toFixed(2)} 秒`);
        console.log(`扫描IP总数: ${ipList.length}`);
        console.log(`找到 ${foundIPs.length} 个匹配的IP地址:`);
        
        if (foundIPs.length > 0) {
            foundIPs.forEach((item, index) => {
                console.log(`${index + 1}. ${item.ip} - ${item.url}`);
            });
            
            // 保存结果到文件
            const result = {
                scanTime: new Date().toISOString(),
                duration: duration,
                totalIPs: ipList.length,
                config: config,
                foundIPs: foundIPs,
                urls: foundIPs.map(item => item.url)
            };
            
            const filename = `scan_results_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
            fs.writeFileSync(filename, JSON.stringify(result, null, 2));
            console.log(`\n结果已保存到 ${filename}`);
        } else {
            console.log('  未找到匹配的IP地址');
        }
        
    } catch (error) {
        console.error('扫描过程中发生错误:', error.message);
    }
}

// 处理Ctrl+C
process.on('SIGINT', () => {
    console.log('\n\n扫描被用户中断');
    console.log(`已找到 ${foundIPs.length} 个匹配的IP地址`);
    if (foundIPs.length > 0) {
        console.log('找到的IP地址:');
        foundIPs.forEach((item, index) => {
            console.log(`${index + 1}. ${item.ip} - ${item.url}`);
        });
    }
    process.exit(0);
});

// 运行主函数
main().catch(console.error);